import os
import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Nombre(s)",
    "Apellido_Paterno",
    "Apellido_Materno",
    "CURP",
    "RFC",
    "Domicilio",
    "Colonia",
    "C.P.",
    "Municipio",
    "Estado"
]

PROMPT = (
    "A continuación tienes el texto extraído de una Cédula de Identificación Fiscal (SAT) mexicana. "
    "Extrae SOLO los datos del contribuyente y su domicilio fiscal, ignorando notas legales y datos de la autoridad. "
    "Devuelve un JSON con los siguientes campos: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, déjalo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Nombre(s)\": \"EFREN\",\n"
    "  \"Apellido_Paterno\": \"CRUZ\",\n"
    "  \"Apellido_Materno\": \"AVILA\",\n"
    "  \"CURP\": \"CUAE860802HJCRVF07\",\n"
    "  \"RFC\": \"CUAE860802178\",\n"
    "  \"Domicilio\": \"BLVD. ALBEROS 580 15B\",\n"
    "  \"Colonia\": \"OTRA NO ESPECIFICADA EN EL CATALOGO\",\n"
    "  \"C.P.\": \"45595\",\n"
    "  \"Municipio\": \"SAN PEDRO TLAQUEPAQUE\",\n"
    "  \"Estado\": \"JALISCO\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_sat(texto_ocr, api_key):
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (SAT) ---\n", content, "\n-----------------------------\n")
    return extraer_json_de_llm(content) 