import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Nombre(s)",
    "Apellido_Paterno",
    "Apellido_Materno",
    "CURP",
    "NSS",
    "<PERSON><PERSON>_de_Nacimiento",
    "Sexo",
    "<PERSON><PERSON>_de_Nacimiento"
]

PROMPT = (
    "A continuación tienes el texto extraído de un documento de Asignación de Número de Seguridad Social (NSS) del IMSS. "
    "Extrae SOLO los datos del trabajador y devuélvelos en formato JSON con los siguientes campos: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, déjalo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Nombre(s)\": \"EFREN\",\n"
    "  \"Apellido_Paterno\": \"CRUZ\",\n"
    "  \"Apellido_Materno\": \"AVILA\",\n"
    "  \"CURP\": \"CUAE860802HJCRVF07\",\n"
    "  \"NSS\": \"04058614522\",\n"
    "  \"Fecha_de_Nacimiento\": \"02/08/1986\",\n"
    "  \"Sexo\": \"HOMBRE\",\n"
    "  \"Lugar_de_Nacimiento\": \"JALISCO\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_nss(texto_ocr, api_key):
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (NSS) ---\n", content, "\n-----------------------------\n")
    return extraer_json_de_llm(content) 