"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from pydantic import model_serializer
from typing_extensions import NotRequired, TypedDict


class OCRImageObjectTypedDict(TypedDict):
    id: str
    r"""Image ID for extracted image in a page"""
    top_left_x: Nullable[int]
    r"""X coordinate of top-left corner of the extracted image"""
    top_left_y: Nullable[int]
    r"""Y coordinate of top-left corner of the extracted image"""
    bottom_right_x: Nullable[int]
    r"""X coordinate of bottom-right corner of the extracted image"""
    bottom_right_y: Nullable[int]
    r"""Y coordinate of bottom-right corner of the extracted image"""
    image_base64: NotRequired[Nullable[str]]
    r"""Base64 string of the extracted image"""


class OCRImageObject(BaseModel):
    id: str
    r"""Image ID for extracted image in a page"""

    top_left_x: Nullable[int]
    r"""X coordinate of top-left corner of the extracted image"""

    top_left_y: Nullable[int]
    r"""Y coordinate of top-left corner of the extracted image"""

    bottom_right_x: Nullable[int]
    r"""X coordinate of bottom-right corner of the extracted image"""

    bottom_right_y: Nullable[int]
    r"""Y coordinate of bottom-right corner of the extracted image"""

    image_base64: OptionalNullable[str] = UNSET
    r"""Base64 string of the extracted image"""

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = ["image_base64"]
        nullable_fields = [
            "top_left_x",
            "top_left_y",
            "bottom_right_x",
            "bottom_right_y",
            "image_base64",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
