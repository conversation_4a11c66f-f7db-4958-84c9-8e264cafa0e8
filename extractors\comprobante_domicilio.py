import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Nombre(s)",
    "Apellido_Paterno",
    "Apellido_Materno",
    "Domicilio",
    "Colonia",
    "C.P.",
    "Municipio",
    "Estado"
]

PROMPT = (
    "A continuación tienes el texto extraído de un Comprobante de Domicilio mexicano (recibo de luz, agua, teléfono, etc.). "
    "Extrae SOLO los datos del titular y la dirección completa, ignorando datos de la empresa emisora y notas legales. "
    "Devuelve un JSON con los siguientes campos: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, déjalo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Nombre(s)\": \"EFREN\",\n"
    "  \"Apellido_Paterno\": \"CRUZ\",\n"
    "  \"Apellido_Materno\": \"AVILA\",\n"
    "  \"Domicilio\": \"PRIV ALBEROS 580 15 B\",\n"
    "  \"Colonia\": \"CARDENAL LAS JUNTAS\",\n"
    "  \"C.P.\": \"45590\",\n"
    "  \"Municipio\": \"TLAQUEPAQUE\",\n"
    "  \"Estado\": \"JALISCO\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_comprobante_domicilio(texto_ocr, api_key):
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (COMPROBANTE DOMICILIO) ---\n", content, "\n-----------------------------\n")
    return extraer_json_de_llm(content) 