import os
import json
import fitz  # PyMuPDF
from mistralai.client import MistralClient as <PERSON><PERSON><PERSON>
from dotenv import load_dotenv
from PIL import Image
import tempfile

def cargar_api_key():
    """
    Carga la API Key de Mistral desde las variables de entorno.
    Lanza un error si no está definida.
    Returns:
        str: API Key de Mistral
    """
    load_dotenv()
    api_key = os.environ.get("MISTRAL_API_KEY")
    if not api_key:
        raise ValueError("Error: La variable de entorno MISTRAL_API_KEY no está definida.")
    return api_key

def convertir_imagen_a_pdf(path_imagen):
    """
    Convierte una imagen (JPG, PNG) a PDF temporal para procesar con OCR.
    Args:
        path_imagen (str): Ruta de la imagen.
    Returns:
        str: Ruta del PDF temporal generado.
    """
    with Image.open(path_imagen) as img:
        img = img.convert("RGB")
        temp_pdf = tempfile.NamedTemporaryFile(delete=False, suffix=".pdf")
        img.save(temp_pdf, format="PDF")
        temp_pdf.close()
        return temp_pdf.name

def extract_text_pymupdf(pdf_path):
    """
    Extrae texto de un PDF usando PyMuPDF (fallback si OCR falla).
    Args:
        pdf_path (str): Ruta del PDF.
    Returns:
        str: Texto extraído.
    """
    doc = fitz.open(pdf_path)
    extracted_text = "\n\n".join([page.get_text() for page in doc])
    return extracted_text.strip()

def ocr_mistral(document_path):
    """
    Realiza OCR sobre un documento usando Mistral OCR. Si falla o no hay texto útil, usa PyMuPDF como fallback.
    Args:
        document_path (str): Ruta del archivo PDF o imagen.
    Returns:
        str: Texto extraído del documento.
    """
    api_key = cargar_api_key()
    client = Mistral(api_key=api_key)
    archivo_a_procesar = document_path
    temp_pdf_path = None
    # Si es imagen, convertir a PDF
    if document_path.lower().endswith((".jpg", ".jpeg", ".png")):
        temp_pdf_path = convertir_imagen_a_pdf(document_path)
        archivo_a_procesar = temp_pdf_path
    try:
        # Subir el archivo y obtener la URL firmada
        with open(archivo_a_procesar, "rb") as f:
            uploaded_pdf = client.files.upload(
                file={
                    "file_name": os.path.basename(archivo_a_procesar),
                    "content": f,
                },
                purpose="ocr"
            )
        signed_url = client.files.get_signed_url(file_id=uploaded_pdf.id)
        # Procesar OCR
        ocr_response = client.ocr.process(
            model="mistral-ocr-latest",
            document={
                "type": "document_url",
                "document_url": signed_url.url
            },
            include_image_base64=False
        )
        # Extraer texto de las páginas
        extracted_text = "\n\n".join(
            [page.markdown for page in ocr_response.pages if hasattr(page, "markdown") and page.markdown]
        )
        # Fallback: si el texto está vacío o solo contiene imágenes, usar PyMuPDF
        if not extracted_text.strip() or all('![' in page.markdown for page in ocr_response.pages if hasattr(page, "markdown")):
            print("⚠️ Mistral no devolvió texto útil, usando PyMuPDF como fallback...")
            extracted_text = extract_text_pymupdf(archivo_a_procesar)
        return extracted_text
    except Exception as e:
        print(f"❌ Error durante el procesamiento OCR: {e}")
        return ""
    finally:
        # Eliminar el PDF temporal si se creó y ya está cerrado
        if temp_pdf_path and os.path.exists(temp_pdf_path):
            try:
                os.remove(temp_pdf_path)
            except Exception as e:
                print(f"⚠️ No se pudo eliminar el archivo temporal: {e}")

# Este archivo está listo para importar la función ocr_mistral en otros módulos.

