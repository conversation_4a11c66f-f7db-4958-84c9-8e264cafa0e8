import json
from mistralai import Mistral
from extractors.utils import extraer_json_de_llm

CAMPOS = [
    "Banco",
    "Cuenta",
    "Cuenta_Clabe"
]

PROMPT = (
    "A continuación tienes el texto extraído de un documento bancario mexicano (carátula de cuenta, estado de cuenta, etc.). "
    "Extrae SOLO los siguientes datos bancarios, si están presentes, y devuélvelos en formato JSON: "
    f"{CAMPOS}. "
    "Si algún campo no está presente, déjalo vacío. "
    "Ejemplo de formato de salida:\n"
    "{\n"
    "  \"Banco\": \"BBVA\",\n"
    "  \"Cuenta\": \"56785195368\",\n"
    "  \"Cuenta_Clabe\": \"012345678901234567\"\n"
    "}\n"
    "Texto extraído:\n"
)

def extract_cuenta(texto_ocr, api_key):
    client = Mistral(api_key=api_key)
    response = client.chat.complete(
        model="mistral-small-latest",
        messages=[{"role": "user", "content": PROMPT + texto_ocr}],
        temperature=0.1,
        max_tokens=512
    )
    content = response.choices[0].message.content
    print("\n--- RESPUESTA CRUDA DEL LLM (CUENTA) ---\n", content, "\n-----------------------------\n")
    return extraer_json_de_llm(content) 