"""Code generated by Speakeasy (https://speakeasy.com). DO NOT EDIT."""

from __future__ import annotations
from .modelcapabilities import ModelCapabilities, ModelCapabilitiesTypedDict
from datetime import datetime
from mistralai.types import BaseModel, Nullable, OptionalNullable, UNSET, UNSET_SENTINEL
from mistralai.utils import validate_const
import pydantic
from pydantic import model_serializer
from pydantic.functional_validators import After<PERSON><PERSON>da<PERSON>
from typing import List, Literal, Optional
from typing_extensions import Annotated, NotRequired, TypedDict


FTModelCardType = Literal["fine-tuned"]


class FTModelCardTypedDict(TypedDict):
    r"""Extra fields for fine-tuned models."""

    id: str
    capabilities: ModelCapabilitiesTypedDict
    job: str
    root: str
    object: NotRequired[str]
    created: NotRequired[int]
    owned_by: NotRequired[str]
    name: NotRequired[Nullable[str]]
    description: NotRequired[Nullable[str]]
    max_context_length: NotRequired[int]
    aliases: NotRequired[List[str]]
    deprecation: NotRequired[Nullable[datetime]]
    default_model_temperature: NotRequired[Nullable[float]]
    type: FTModelCardType
    archived: NotRequired[bool]


class FTModelCard(BaseModel):
    r"""Extra fields for fine-tuned models."""

    id: str

    capabilities: ModelCapabilities

    job: str

    root: str

    object: Optional[str] = "model"

    created: Optional[int] = None

    owned_by: Optional[str] = "mistralai"

    name: OptionalNullable[str] = UNSET

    description: OptionalNullable[str] = UNSET

    max_context_length: Optional[int] = 32768

    aliases: Optional[List[str]] = None

    deprecation: OptionalNullable[datetime] = UNSET

    default_model_temperature: OptionalNullable[float] = UNSET

    TYPE: Annotated[
        Annotated[
            Optional[FTModelCardType], AfterValidator(validate_const("fine-tuned"))
        ],
        pydantic.Field(alias="type"),
    ] = "fine-tuned"

    archived: Optional[bool] = False

    @model_serializer(mode="wrap")
    def serialize_model(self, handler):
        optional_fields = [
            "object",
            "created",
            "owned_by",
            "name",
            "description",
            "max_context_length",
            "aliases",
            "deprecation",
            "default_model_temperature",
            "type",
            "archived",
        ]
        nullable_fields = [
            "name",
            "description",
            "deprecation",
            "default_model_temperature",
        ]
        null_default_fields = []

        serialized = handler(self)

        m = {}

        for n, f in self.model_fields.items():
            k = f.alias or n
            val = serialized.get(k)
            serialized.pop(k, None)

            optional_nullable = k in optional_fields and k in nullable_fields
            is_set = (
                self.__pydantic_fields_set__.intersection({n})
                or k in null_default_fields
            )  # pylint: disable=no-member

            if val is not None and val != UNSET_SENTINEL:
                m[k] = val
            elif val != UNSET_SENTINEL and (
                not k in optional_fields or (optional_nullable and is_set)
            ):
                m[k] = val

        return m
